name: melodyze_demo
description: A demo Flutter app for scrolling lyrics with audio.
publish_to: 'none'
version: 1.0.0+1

flutter_version: "3.29.1"

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  audioplayers: ^6.5.0
  file_picker: ^8.1.2
  image_picker: ^1.0.4
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  shared_preferences: ^2.2.2
  scrollable_positioned_list: ^0.3.8
  camera: ^0.10.5+9
  video_player: ^2.8.1
  media_store_plus: ^0.1.3
  share_plus: ^7.2.1
  device_info_plus: ^10.0.0
  just_audio: ^0.10.4

  # FFmpegKit with drawtext support
  ffmpeg_kit_flutter_new: ^3.1.0
  # ffmpeg_kit_flutter_new_full: ^1.0.0
  # ffmpeg_kit_flutter_new_video: ^1.0.1


  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/dummy_audio.mp3
    - assets/dummy_dp.jpg
    - assets/lyrics.json
    - assets/canva_templates.json
    - assets/MicrosoftTeams-video.mp4
    - assets/profile_border.png
    - assets/logo_melodyze.png
  fonts:
    - family: Ethnocentric
      fonts:
        - asset: assets/fonts/ethnocentric_rg.otf
          weight: 400
        - asset: assets/fonts/ethnocentric_rg_it.otf
          weight: 400
          style: italic