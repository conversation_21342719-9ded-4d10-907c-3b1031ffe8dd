import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:melodyze_demo/ffmpeg_video_creator/canvas_video_creator.dart';
import 'package:melodyze_demo/video_preview_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'ffmpeg_video_creator/models.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({Key? key}) : super(key: key);

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  File? selectedLyricsFile;
  File? selectedAudioFile;
  File? selectedImageFile;
  String username = '';
  String trackName = '';
  String originalArtist = '';

  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _trackNameController = TextEditingController();
  final TextEditingController _originalArtistController = TextEditingController();
  bool isLoading = false;

  Future<void> _pickLyricsFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null) {
        setState(() {
          selectedLyricsFile = File(result.files.single.path!);
        });
        await _validateLyricsFile();
      }
    } catch (e) {
      _showErrorDialog('Error selecting lyrics file: $e');
    }
  }

  Future<void> _validateLyricsFile() async {
    if (selectedLyricsFile == null) return;

    try {
      final jsonString = await selectedLyricsFile!.readAsString();
      final Map<String, dynamic> data = json.decode(jsonString);

      if (data.containsKey('lyrics') && data.containsKey('countdown')) {
        final lyricsData = data['lyrics']?['data'];
        if (lyricsData is List && lyricsData.isNotEmpty) {
          for (var item in lyricsData) {
            if (!item.containsKey('text') || !item.containsKey('start_time')) {
              throw Exception('Invalid lyrics format. Each item should have "text" and "start_time" fields.');
            }
          }
          _showSuccessSnackBar('Lyrics file validated successfully!');
        } else {
          throw Exception('No lyrics data found in the file.');
        }
      } else {
        final List<dynamic> oldFormat = json.decode(jsonString);
        for (var item in oldFormat) {
          if (!item.containsKey('time') || !item.containsKey('line')) {
            throw Exception('Invalid lyrics format. Each item should have "time" and "line" fields.');
          }
        }
        _showSuccessSnackBar('Lyrics file validated successfully!');
      }
    } catch (e) {
      setState(() {
        selectedLyricsFile = null;
      });
      _showErrorDialog('Invalid lyrics format: $e');
    }
  }

  Future<void> _pickAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
      );

      if (result != null) {
        setState(() {
          selectedAudioFile = File(result.files.single.path!);
        });
        _showSuccessSnackBar('Audio file selected successfully!');
      }
    } catch (e) {
      _showErrorDialog('Error selecting audio file: $e');
    }
  }

  Future<void> _pickImageFile() async {
    try {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Image Source'),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.blue),
                title: const Text('Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromSource(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.blue),
                title: const Text('Camera'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromSource(ImageSource.camera);
                },
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      _showErrorDialog('Error selecting image: $e');
    }
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    final XFile? image = await _imagePicker.pickImage(
      source: source,
      maxWidth: 300,
      maxHeight: 300,
      imageQuality: 80,
    );

    if (image != null) {
      setState(() {
        selectedImageFile = File(image.path);
      });
      // _showSuccessSnackBar('Profile image selected successfully!');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _createVideo() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Verify that all files exist and are readable
      if (!await selectedLyricsFile!.exists()) {
        throw FileSystemException('Lyrics file not found');
      }
      if (!await selectedAudioFile!.exists()) {
        throw FileSystemException('Audio file not found');
      }
      if (!await selectedImageFile!.exists()) {
        throw FileSystemException('Image file not found');
      }

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _VideoCreationDialog(
          lyricsFile: selectedLyricsFile!,
          audioFile: selectedAudioFile!,
          imageFile: selectedImageFile!,
          username: username.trim(),
          trackName: trackName.trim().isEmpty ? 'Unknown Track' : trackName.trim(),
          originalArtist: originalArtist.trim().isEmpty ? 'Unknown Artist' : originalArtist.trim(),
        ),
      );
    } catch (e) {
      _showErrorDialog('Error preparing video creation: ${e.toString()}');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  bool get _allFilesSelected => selectedLyricsFile != null && selectedAudioFile != null && selectedImageFile != null;
  // &&
  // username.trim().isNotEmpty &&
  // trackName.trim().isNotEmpty &&
  // originalArtist.trim().isNotEmpty;

  @override
  void dispose() {
    _usernameController.dispose();
    _trackNameController.dispose();
    _originalArtistController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Lyric Video'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.blue.shade50, Colors.white],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Create Your Lyric Video',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade900,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                _buildInputCard(
                  title: 'Username',
                  icon: Icons.person,
                  controller: _usernameController,
                  hintText: 'Enter your username',
                  prefixText: '@Ani',
                  onChanged: (value) => setState(() => username = value),
                  isValid: username.trim().isNotEmpty,
                  maxLength: 20,
                ),
                const SizedBox(height: 16),
                _buildInputCard(
                  title: 'Track Name',
                  prefixText: 'Faded',
                  icon: Icons.music_note,
                  controller: _trackNameController,
                  hintText: 'Enter track name',
                  onChanged: (value) => setState(() => trackName = value),
                  isValid: trackName.trim().isNotEmpty,
                  maxLength: 50,
                ),
                const SizedBox(height: 16),
                _buildInputCard(
                  title: 'Original Artist',
                  prefixText: 'Alan Walker',
                  icon: Icons.person_outline,
                  controller: _originalArtistController,
                  hintText: 'Enter original artist name',
                  onChanged: (value) => setState(() => originalArtist = value),
                  isValid: originalArtist.trim().isNotEmpty,
                  maxLength: 50,
                ),
                const SizedBox(height: 16),
                _buildFileSelectionCard(
                  title: 'Lyrics File (JSON)',
                  subtitle: 'Select a JSON file with lyrics and timing',
                  icon: Icons.text_fields,
                  file: selectedLyricsFile,
                  onTap: _pickLyricsFile,
                ),
                const SizedBox(height: 16),
                _buildFileSelectionCard(
                  title: 'Audio File',
                  subtitle: 'Select an audio file (MP3, WAV, etc.)',
                  icon: Icons.audio_file,
                  file: selectedAudioFile,
                  onTap: _pickAudioFile,
                ),
                const SizedBox(height: 16),
                _buildFileSelectionCard(
                  title: 'Profile Image',
                  subtitle: 'Select or capture a profile image',
                  icon: Icons.image,
                  file: selectedImageFile,
                  onTap: _pickImageFile,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _allFilesSelected && !isLoading ? _createVideo : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                    shadowColor: Colors.blue.shade200,
                  ),
                  child: isLoading
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                            SizedBox(width: 12),
                            Text('Processing...', style: TextStyle(fontSize: 16)),
                          ],
                        )
                      : const Text(
                          'Create Lyric Video',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputCard({
    required String title,
    required IconData icon,
    required TextEditingController controller,
    required String hintText,
    String? prefixText,
    required Function(String) onChanged,
    required bool isValid,
    required int maxLength,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isValid ? Colors.green.shade400 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isValid ? Colors.green.shade400 : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isValid ? Colors.white : Colors.grey.shade600,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade900,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller,
                    onChanged: onChanged,
                    decoration: InputDecoration(
                      hintText: hintText,
                      prefixText: prefixText,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.blue.shade700, width: 2),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      hintStyle: TextStyle(color: Colors.grey.shade400),
                    ),
                    style: const TextStyle(fontSize: 14),
                    maxLength: maxLength,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required File? file,
    required VoidCallback onTap,
  }) {
    final bool isSelected = file != null;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? Colors.green.shade400 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.green.shade400 : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade900,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isSelected ? file.path.split(Platform.pathSeparator).last : subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: isSelected ? Colors.green.shade600 : Colors.grey.shade500,
                        fontStyle: isSelected ? FontStyle.normal : FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                isSelected ? Icons.check_circle : Icons.add_circle_outline,
                color: isSelected ? Colors.green.shade400 : Colors.grey.shade400,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _VideoCreationDialog extends StatefulWidget {
  final File lyricsFile;
  final File audioFile;
  final File imageFile;
  final String username;
  final String trackName;
  final String originalArtist;

  const _VideoCreationDialog({
    required this.lyricsFile,
    required this.audioFile,
    required this.imageFile,
    required this.username,
    required this.trackName,
    required this.originalArtist,
  });

  @override
  State<_VideoCreationDialog> createState() => _VideoCreationDialogState();
}

class _VideoCreationDialogState extends State<_VideoCreationDialog> {
  String currentMessage = "Preparing video creation...";
  double progress = 0.0;
  bool hasError = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _startVideoCreation();
  }

  Future<void> _startVideoCreation() async {
    try {
      // final tempDir = await getTemporaryDirectory();
      // final outputDir = Directory('${tempDir.path}/lyric_videos');

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final dir = await getApplicationDocumentsDirectory();
      final outputPath = '${dir.path}/lyric_video_$timestamp.mp4';
      log("Output path: $outputPath");

      final params = VideoCreationParams(
        audioFile: widget.audioFile.path,
        userImageFile: widget.imageFile.path,
        borderFile: await extractAsset('assets/profile_border.png'),
        brandLogoFile: await extractAsset('assets/logo_melodyze.png'),
        waveformVideo: await extractAsset('assets/MicrosoftTeams-video.mp4'),
        lyricsFile: widget.lyricsFile.path,
        trackName: widget.trackName,
        username: widget.username,
        originalArtist: widget.originalArtist,
        outputPath: outputPath,
        fontPath: await extractAsset('assets/fonts/ethnocentric_rg.otf'),
        arialFontPath: await extractAsset('assets/fonts/ethnocentric_rg_it.otf'),
      );

      CanvasVideoCreator.createLyricVideo(
          params: params,
          onSuccess: (path) {
            // Video creation successful, navigate to preview screen
            if (mounted) {
              currentMessage = "Video created successfully!";
              log("Video creation successful, navigating to preview screen...");
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => VideoPreviewScreen(
                    videoPath: outputPath,
                    username: widget.username,
                  ),
                ),
              );
            }
          },
          onError: (error) {
            if (mounted) {
              setState(() {
                hasError = true;
                errorMessage = error;
                currentMessage = "Error creating video";
              });
            }
          });
    } catch (e) {
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage = e.toString();
          currentMessage = "Error creating video";
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print("Error message ======= : $errorMessage");
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Text(
        'Creating Video',
        style: TextStyle(color: Colors.blue.shade900, fontWeight: FontWeight.w600),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!hasError) ...[
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade700),
            ),
            const SizedBox(height: 16),
            Text(
              currentMessage,
              style: TextStyle(color: Colors.grey.shade700),
              textAlign: TextAlign.center,
            ),
          ] else ...[
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Error: $errorMessage',
              style: TextStyle(color: Colors.red.shade700),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
      actions: hasError
          ? [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('OK', style: TextStyle(color: Colors.blue.shade700)),
              ),
            ]
          : null,
    );
  }
}
