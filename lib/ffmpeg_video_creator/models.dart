import 'dart:developer';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:just_audio/just_audio.dart';

Future<String> extractAsset(String assetPath) async {
  try {
    final docsDir = await getApplicationDocumentsDirectory();
    final assetsDir = Directory('${docsDir.path}/app_assets');

    // Ensure assets directory exists
    if (!await assetsDir.exists()) {
      await assetsDir.create(recursive: true);
    }

    // Create a safe filename from the asset path
    final fileName = assetPath.split('/').last;
    String path = "${assetsDir.path}/$fileName";

    final file = File(path);
    if (await file.exists()) {
      return path;
    }

    // Load and write the asset
    ByteData data = await rootBundle.load(assetPath);
    final buffer = data.buffer;
    await file.writeAsBytes(buffer.asUint8List(data.offsetInBytes, data.lengthInBytes), flush: true // Ensure the write completes
        );

    return path;
  } catch (e) {
    throw Exception('Failed to extract asset $assetPath: $e');
  }
}

Future<double> getAudioDuration(String path) async {
  final player = AudioPlayer();

  try {
    await player.setUrl(path); // Or use setFilePath(path) for local files

    // Wait for the duration to be available via the stream
    final duration = await player.durationStream.firstWhere((d) => d != null);

    log('Duration: ${duration?.inSeconds} seconds');
    return duration?.inSeconds.toDouble() ?? 0;
  } catch (e) {
    log('Error loading audio: $e');
    return 0.0;
  } finally {
    await player.dispose();
  }
}
