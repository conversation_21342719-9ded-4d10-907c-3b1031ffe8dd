// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
// import 'package:melodyze_demo/ffmpeg_video_creator/models.dart';
// // import 'package:ffmpeg_kit_flutter_new_full/ffmpeg_kit.dart';
// // import 'package:ffmpeg_kit_flutter_new_video/ffmpeg_kit.dart';
// // import 'package:ffmpeg_kit_flutter_new_full/return_code.dart';

// class VideoCreationParams {
//   final String audioFile;
//   final String userImageFile;
//   final String borderFile;
//   final String brandLogoFile;
//   final String lyricsFile;
//   final String waveformVideo;
//   final String trackName;
//   final String username;
//   final String originalArtist;
//   final String outputPath;
//   final String fontPath;
//   final String arialFontPath;

//   VideoCreationParams({
//     required this.audioFile,
//     required this.userImageFile,
//     required this.borderFile,
//     required this.brandLogoFile,
//     required this.lyricsFile,
//     required this.waveformVideo,
//     required this.trackName,
//     required this.username,
//     required this.originalArtist,
//     required this.outputPath,
//     required this.fontPath,
//     required this.arialFontPath,
//   });
// }

// class SimplifiedLyricVideoCreator {
//   static Future<void> createLyricVideo({
//     required VideoCreationParams params,
//   }) async {
//     final jsonContent = await File(params.lyricsFile).readAsString();
//     final Map<String, dynamic> lyricsJson = json.decode(jsonContent);
//     final List<dynamic> lyricsData = lyricsJson["lyrics"]["data"] ?? [];

//     double parseTime(String ts) {
//       final parts = ts.split(":");
//       if (parts.length != 3) return 0.0;
//       return int.parse(parts[0]) * 60 + int.parse(parts[1]) + int.parse(parts[2]) / 1000.0;
//     }

//     String escapeForDrawText(String text) {
//       return text.replaceAll("'", "’").replaceAll(":", "\\:").replaceAll(",", "\\,").replaceAll("%", "\\%").replaceAll("@", "\\@");
//     }

//     List<String> wrapLyricLine(String text, {int maxChars = 25}) {
//       List<String> words = text.split(' ');
//       List<String> lines = [];
//       String currentLine = "";
//       for (String word in words) {
//         if (currentLine.isEmpty) {
//           currentLine = word;
//         } else if ((currentLine + " " + word).length <= maxChars) {
//           currentLine += " $word";
//         } else {
//           lines.add(currentLine);
//           currentLine = word;
//         }
//       }
//       if (currentLine.isNotEmpty) {
//         lines.add(currentLine);
//       }
//       return lines;
//     }

//     final int BG_W = 720;
//     final int BG_H = 1360;
//     final int IMG_SIZE = 240;
//     final int USER_X = ((BG_W - IMG_SIZE) / 6).floor();
//     final int USER_Y = (BG_H * 0.15 - IMG_SIZE / 2).floor();
//     final int LOGO_SIZE = 120;
//     final int LOGO_X = USER_X + IMG_SIZE - (LOGO_SIZE ~/ 2) - 40;
//     final int LOGO_Y = USER_Y + IMG_SIZE - (LOGO_SIZE ~/ 2) - 20;
//     final double TEXT_X = (BG_W - IMG_SIZE) / 1.7;
//     final double TEXT_Y = USER_Y + 40.0;
//     final int waveformHeight = (BG_H * 0.22).floor();
//     final int waveformY = ((BG_H - waveformHeight) / 2.25).floor();
//     final int lyricsStartY = waveformY + waveformHeight + 250;

//     const int blockHeight = 120;
//     const int lineHeight = 46;
//     const int lineHeightOther = 36;
//     const int fontSizeCurrent = 42;
//     const int fontSizeOther = 24;
//     const String colorCurrent = "#ECAED9DE";
//     const String colorOther = "#ECAED961";

//     final durationSeconds = await getAudioDuration(params.audioFile);

//     List<String> fc = [];
//     fc.add("color=black:s=${BG_W}x${BG_H}:d=${durationSeconds.toStringAsFixed(3)}[bg]");
//     fc.add(
//         "[1:v]scale=${IMG_SIZE}:${IMG_SIZE},format=yuva444p,geq=lum='p(X,Y)':a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'[usercircle]");
//     fc.add("[2:v]scale=${IMG_SIZE + 18}:${IMG_SIZE + 18}[border]");
//     fc.add("[bg][border]overlay=x=${USER_X - 10}:y=${USER_Y - 10}[step0]");
//     fc.add("[step0][usercircle]overlay=x=${USER_X}:y=${USER_Y}[step1]");
//     fc.add("[3:v]scale=${LOGO_SIZE}:${LOGO_SIZE}[logo]");
//     fc.add("[step1][logo]overlay=x=${LOGO_X}:y=${LOGO_Y}[step2]");
//     fc.add("[4:v]scale=${BG_W}:${waveformHeight}[waveform_scaled]");
//     fc.add("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]");
//     fc.add("[step2][waveform]overlay=x=0:y=${waveformY}:repeatlast=0[step2_waveform]");
    
//     fc.add(
//         "[step2_waveform]drawtext=fontfile='${params.fontPath}':text='${escapeForDrawText(params.trackName)}':fontcolor=#FEFEFECC:fontsize=40:x=${TEXT_X}:y=${TEXT_Y}[tt1]");
//     fc.add(
//         "[tt1]drawtext=fontfile='${params.arialFontPath}':text='${escapeForDrawText(params.originalArtist.toUpperCase())}':fontcolor=#FEFEFECC:fontsize=32:x=${TEXT_X}:y=${TEXT_Y + 50}[tt2]");
//     fc.add(
//         "[tt2]drawtext=fontfile='${params.arialFontPath}':text='${escapeForDrawText(params.username)}':fontcolor=#FEFEFECC:fontsize=36:x=${TEXT_X}:y=${TEXT_Y + 140}[step3]");

//     String currentLabel = "step3";
//     int filterCount = 0;

//     if (lyricsData.isNotEmpty) {
//       final first = lyricsData[0];
//       final firstStart = parseTime(first["start_time"]);
//       final firstLines = wrapLyricLine(first["text"], maxChars: 20).map(escapeForDrawText).toList();
//       for (int j = 0; j < firstLines.length; j++) {
//         final y = lyricsStartY + blockHeight + j * lineHeight;
//         final size = fontSizeCurrent;
//         filterCount++;
//         final label = "lyric$filterCount";
//         fc.add(
//             "[${currentLabel}]drawtext=fontfile='${params.fontPath}':text='${firstLines[j]}':fontcolor=${colorOther}:fontsize=${size}:x=(w-text_w)/2:y=${y}:enable='between(t,0,${firstStart})'[${label}]");
//         currentLabel = label;
//       }
//     }

//     for (int i = 0; i < lyricsData.length; i++) {
//       final item = lyricsData[i];
//       final start = parseTime(item["start_time"]);
//       double end = parseTime(item["end_time"]);
//       if (end == 0.0 && i == lyricsData.length - 1) end = durationSeconds;
//       final lines = wrapLyricLine(item["text"], maxChars: 20).map(escapeForDrawText).toList();
//       for (int j = 0; j < lines.length; j++) {
//         final y = lyricsStartY + blockHeight + j * lineHeight;
//         filterCount++;
//         final label = "lyric$filterCount";
//         fc.add(
//             "[${currentLabel}]drawtext=fontfile='${params.fontPath}':text='${lines[j]}':fontcolor=${colorCurrent}:fontsize=${fontSizeCurrent}:x=(w-text_w)/2:y=${y}:enable='between(t,${start},${end})'[${label}]");
//         currentLabel = label;
//       }

//       if (i > 0) {
//         final prevLines = wrapLyricLine(lyricsData[i - 1]["text"], maxChars: 25).map(escapeForDrawText).toList();
//         for (int j = 0; j < prevLines.length; j++) {
//           final y = lyricsStartY + j * lineHeightOther;
//           filterCount++;
//           final label = "lyric$filterCount";
//           fc.add(
//               "[${currentLabel}]drawtext=fontfile='${params.fontPath}':text='${prevLines[j]}':fontcolor=${colorOther}:fontsize=${fontSizeOther}:x=(w-text_w)/2:y=${y}:enable='between(t,${start},${end})'[${label}]");
//           currentLabel = label;
//         }
//       }

//       if (i < lyricsData.length - 1) {
//         final nextLines = wrapLyricLine(lyricsData[i + 1]["text"], maxChars: 25).map(escapeForDrawText).toList();
//         for (int j = 0; j < nextLines.length; j++) {
//           final y = lyricsStartY + 2 * 140 + j * lineHeightOther;
//           filterCount++;
//           final label = "lyric$filterCount";
//           fc.add(
//               "[${currentLabel}]drawtext=fontfile='${params.fontPath}':text='${nextLines[j]}':fontcolor=${colorOther}:fontsize=${fontSizeOther}:x=(w-text_w)/2:y=${y}:enable='between(t,${start},${end})'[${label}]");
//           currentLabel = label;
//         }
//       }
//     }

//     final String filterComplexArg = fc.join(";");
//     final String cmd = "-y -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=48000 "
//         "-i ${params.userImageFile} -i ${params.borderFile} -i ${params.brandLogoFile} -i ${params.waveformVideo} -i ${params.audioFile} "
//         "-filter_complex \"$filterComplexArg\" -map [$currentLabel] -map 5:a -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -c:a aac -b:a 128k ${params.outputPath}";

//     log("Running FFmpeg command: $cmd");
//     final session = await FFmpegKit.execute(cmd);
//     final returnCode = await session.getReturnCode();
//     // if (!(returnCode?.isValueSuccess() ?? false)) {
//       final logs = await session.getAllLogsAsString();
//       final output = await session.getOutput();
//       final failStack = await session.getFailStackTrace();

//     //   log("❌ FFmpeg failed");
//       log("Return Code: $returnCode");
//       log("FFmpeg Output:\n$output");
//       log("FFmpeg Logs:\n$logs");
//       log("FFmpeg Stacktrace:\n$failStack");

//     //   throw Exception("FFmpeg failed with return code $returnCode");
//     // } else {
//     //   log("❌ FFmpeg failed");
//     //   log(await session.getAllLogsAsString() ?? "");
//     //   throw Exception("FFmpeg failed with return code $returnCode");
//     // }
//   }
// }


    
//     // final String cmd = "-y -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 "
//     // "-i ${params.userImageFile} -i ${params.borderFile} -i ${params.brandLogoFile} "
//     // "-i ${params.waveformVideo} -i ${params.audioFile} "
//     // "-filter_complex \"$filterComplexArg\" "
//     // "-map \"[step2_waveform]\" -map 5:a "
//     // "-c:v libx264 -preset ultrafast -crf 20 -pix_fmt yuv420p "
//     // "-c:a aac -b:a 128k ${params.outputPath}";

//     // final String cmd = "-y -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 "
//     // "-i ${params.userImageFile} -i ${params.borderFile} -i ${params.brandLogoFile} "
//     // "-i ${params.waveformVideo} -i ${params.audioFile} "
//     // "-filter_complex \"$filterComplexArg;[step2_waveform]drawtext=text='hello world':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=30[texted]\" "
//     // "-map \"[texted]\" -map 5:a "
//     // "-c:v libx264 -preset ultrafast -crf 20 -pix_fmt yuv420p "
//     // "-c:a aac -b:a 128k ${params.outputPath}";


//     // final String testCmd = "-f lavfi -i color=c=black:s=1280x720:d=50 -i ${params.audioFile} -shortest -c:v libx264 -c:a aac -b:a 192k ${params.outputPath}";
