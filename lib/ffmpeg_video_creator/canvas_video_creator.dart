import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';
import 'package:melodyze_demo/ffmpeg_video_creator/models.dart';

class VideoCreationParams {
  final String audioFile;
  final String userImageFile;
  final String borderFile;
  final String brandLogoFile;
  final String lyricsFile;
  final String waveformVideo;
  final String trackName;
  final String username;
  final String originalArtist;
  final String outputPath;
  final String fontPath;
  final String arialFontPath;

  VideoCreationParams({
    required this.audioFile,
    required this.userImageFile,
    required this.borderFile,
    required this.brandLogoFile,
    required this.lyricsFile,
    required this.waveformVideo,
    required this.trackName,
    required this.username,
    required this.originalArtist,
    required this.outputPath,
    required this.fontPath,
    required this.arialFontPath,
  });
}

// class CompleteCallback {
//   final Function(String) onSuccess;
//   final Function(String) onError;

//   CompleteCallback({
//     required this.onSuccess,
//     required this.onError,
//   });

// }
class CanvasVideoCreator {
  // Video dimensions and layout constants - REDUCED FOR SPEED
  static const int bgWidth = 540; // Reduced from 720
  static const int bgHeight = 960; // Reduced from 1360
  static const int imageSize = 180; // Reduced from 240
  static const int logoSize = 90; // Reduced from 120
  static const int blockHeight = 90; // Reduced from 120
  static const int lineHeightCurrent = 35; // Reduced from 46
  static const int lineHeightOther = 27; // Reduced from 36
  static const int fontSizeCurrent = 32; // Reduced from 42
  static const int fontSizeOther = 18; // Reduced from 24

  // Colors (ARGB format)
  static const String colorCurrentHex = "#ECAED9DE";
  static const String colorOtherHex = "#ECAED961";
  static const String colorMetaHex = "#FEFEFECC";

  /// Main entry point for creating lyric video
  static void createLyricVideo({
    required VideoCreationParams params,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    try {
      log("Starting canvas-based video creation with lyric overlays...");

      // Parse lyrics data
      final lyricsData = await _parseLyricsFile(params.lyricsFile);
      final durationSeconds = await getAudioDuration(params.audioFile);

      log("Audio duration: ${durationSeconds}s, Lyrics count: ${lyricsData.length}");

      // Calculate layout positions
      final layout = _calculateLayout();

      // Build lyric timing specs
      final specs = _generateLyricSpecs(lyricsData, durationSeconds);

      // Prepare temp dir and fonts, render overlays as transparent PNGs
      final tmpDir = await _createTempDirectory();
      final mainFontFamily = await _registerFont(params.fontPath);
      final otherFontFamily = await _registerFont(params.arialFontPath);

      // Meta overlay (track/artist/username)
      final metaPng = await _renderMetaOverlay(
        layout: layout,
        trackName: params.trackName,
        originalArtist: params.originalArtist,
        username: params.username,
        mainFontFamily: mainFontFamily,
        otherFontFamily: otherFontFamily,
        outDir: tmpDir,
      );

      // Lyric overlays
      final lyricPngs = await _renderLyricOverlays(
        specs: specs,
        layout: layout,
        mainFontFamily: mainFontFamily,
        otherFontFamily: otherFontFamily,
        outDir: tmpDir,
      );

      // Compose video with overlays at correct times
      _executeSimpleFFmpegCommand(
        params: params,
        layout: layout,
        durationSeconds: durationSeconds,
        lyricSpecs: specs,
        lyricPngs: lyricPngs,
        metaPng: metaPng,
        onSuccess: onSuccess,
        onError: onError,
      );

      // Cleanup temp dir
      // try {
      //   await tmpDir.delete(recursive: true);
      // } catch (_) {}

      log("Video creation completed successfully!");
    } catch (e, stackTrace) {
      log("Error in video creation: $e");
      log("Stack trace: $stackTrace");
      rethrow;
    }
  }

  // Helper methods

  /// Parse lyrics file and return structured data
  static Future<List<dynamic>> _parseLyricsFile(String lyricsFile) async {
    final jsonContent = await File(lyricsFile).readAsString();
    final Map<String, dynamic> lyricsJson = json.decode(jsonContent);
    return (lyricsJson["lyrics"]["data"] ?? []) as List<dynamic>;
  }

  /// Calculate layout positions based on video dimensions
  static _VideoLayout _calculateLayout() {
    final userX = ((bgWidth - imageSize) / 6).floor();
    final userY = (bgHeight * 0.15 - imageSize / 2).floor();
    final logoX = userX + imageSize - (logoSize ~/ 2) - 40;
    final logoY = userY + imageSize - (logoSize ~/ 2) - 20;
    final textX = (bgWidth - imageSize) / 1.7;
    final textY = userY + 40.0;
    final waveformHeight = (bgHeight * 0.22).floor();
    final waveformY = ((bgHeight - waveformHeight) / 2.25).floor();
    final lyricsStartY = waveformY + waveformHeight + 250;

    return _VideoLayout(
      userX: userX,
      userY: userY,
      logoX: logoX,
      logoY: logoY,
      textX: textX,
      textY: textY,
      waveformHeight: waveformHeight,
      waveformY: waveformY,
      lyricsStartY: lyricsStartY,
    );
  }

  /// Create temporary directory for image rendering
  static Future<Directory> _createTempDirectory() async {
    final tempDir = Directory("${Directory.systemTemp.path}/melodyze_canvas_${DateTime.now().millisecondsSinceEpoch}");
    if (!await tempDir.exists()) {
      await tempDir.create(recursive: true);
    }
    return tempDir;
  }

  /// Register font family from file path
  static Future<String> _registerFont(String fontPath) async {
    final fontFamily = "Font${_generateHash(fontPath)}";
    final loader = FontLoader(fontFamily);
    final bytes = await File(fontPath).readAsBytes();
    loader.addFont(Future.value(bytes.buffer.asByteData()));
    await loader.load();
    return fontFamily;
  }

  /// Generate simple hash for font family names
  static String _generateHash(String input) {
    int hash = 0;
    for (int i = 0; i < input.length; i++) {
      hash = ((hash << 5) - hash + input.codeUnitAt(i)) & 0xffffffff;
    }
    return hash.abs().toString();
  }

  /// Parse time string to seconds
  static double _parseTime(String timeString) {
    final parts = timeString.split(":");
    if (parts.length != 3) return 0.0;
    return int.parse(parts[0]) * 60 + int.parse(parts[1]) + (int.parse(parts[2]) / 1000.0);
  }

  /// Wrap text into multiple lines
  static List<String> _wrapText(String text, {int maxChars = 25}) {
    final words = text.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).toList();
    final lines = <String>[];
    var current = "";
    for (final word in words) {
      if (current.isEmpty) {
        current = word;
      } else if ((current.length + 1 + word.length) <= maxChars) {
        current = "$current $word";
      } else {
        lines.add(current);
        current = word;
      }
    }
    if (current.isNotEmpty) lines.add(current);
    return lines;
  }

  /// Parse color from hex string
  static ui.Color _parseColor(String hex) {
    String h = hex.replaceAll("#", "");
    if (h.length == 8) {
      final a = int.parse(h.substring(0, 2), radix: 16);
      final r = int.parse(h.substring(2, 4), radix: 16);
      final g = int.parse(h.substring(4, 6), radix: 16);
      final b = int.parse(h.substring(6, 8), radix: 16);
      return ui.Color.fromARGB(a, r, g, b);
    }
    if (h.length == 6) {
      final r = int.parse(h.substring(0, 2), radix: 16);
      final g = int.parse(h.substring(2, 4), radix: 16);
      final b = int.parse(h.substring(4, 6), radix: 16);
      return ui.Color.fromARGB(0xFF, r, g, b);
    }
    return const ui.Color(0xFFFFFFFF);
  }

  /// Render static metadata (track/artist/username) overlay as transparent PNG
  static Future<String> _renderMetaOverlay({
    required _VideoLayout layout,
    required String trackName,
    required String originalArtist,
    required String username,
    required String mainFontFamily,
    required String otherFontFamily,
    required Directory outDir,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder);

    double dy = 0;
    final ui.Color color = _parseColor(colorMetaHex);

    void drawLine(String text, double fontSize, String family, double offsetY) {
      final tp = TextPainter(
        text: TextSpan(
          text: text,
          style: TextStyle(
            color: color,
            fontSize: fontSize,
            fontFamily: family,
            height: 1.2,
          ),
        ),
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.left,
        maxLines: 2,
      );
      tp.layout(maxWidth: bgWidth * 0.55);
      tp.paint(canvas, ui.Offset(layout.textX, layout.textY + offsetY));
    }

    drawLine(trackName, 36, mainFontFamily, dy);
    dy += 42;
    drawLine(originalArtist.toUpperCase(), 28, otherFontFamily, dy);
    dy += 38;
    drawLine('@$username', 30, otherFontFamily, dy);

    final image = await recorder.endRecording().toImage(bgWidth, bgHeight);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final path = '${outDir.path}/meta.png';
    await File(path).writeAsBytes(byteData!.buffer.asUint8List(), flush: true);
    return path;
  }

  /// Render lyric overlays into transparent PNG images the size of the canvas
  static Future<List<String>> _renderLyricOverlays({
    required List<_LyricSpec> specs,
    required _VideoLayout layout,
    required String mainFontFamily,
    required String otherFontFamily,
    required Directory outDir,
  }) async {
    final currentColor = _parseColor(colorCurrentHex);
    final otherColor = _parseColor(colorOtherHex);

    final List<String> paths = [];

    for (int i = 0; i < specs.length; i++) {
      final spec = specs[i];

      final recorder = ui.PictureRecorder();
      final canvas = ui.Canvas(recorder);

      // helper to draw centered multi-line text
      void drawCentered(String text, double y, double fontSize, ui.Color color, String family, double maxWidth) {
        final tp = TextPainter(
          text: TextSpan(
            text: text,
            style: TextStyle(
              color: color,
              fontSize: fontSize,
              fontFamily: family,
              height: 1.25,
              letterSpacing: 0.2,
            ),
          ),
          textDirection: ui.TextDirection.ltr,
          textAlign: TextAlign.center,
          maxLines: 2,
        );
        tp.layout(maxWidth: maxWidth);
        final dx = (bgWidth - tp.width) / 2;
        tp.paint(canvas, ui.Offset(dx, y));
      }

      final double prevTop = layout.lyricsStartY.toDouble();
      final double currentTop = (layout.lyricsStartY + blockHeight).toDouble();
      final double nextTop = (layout.lyricsStartY + blockHeight * 2 + 20).toDouble();
      final double maxWidth = bgWidth * 0.9;

      // draw prev/current/next blocks
      for (int j = 0; j < spec.prevLines.length; j++) {
        drawCentered(spec.prevLines[j], prevTop + j * lineHeightOther, fontSizeOther.toDouble(), otherColor, otherFontFamily, maxWidth);
      }
      for (int j = 0; j < spec.currentLines.length; j++) {
        drawCentered(spec.currentLines[j], currentTop + j * lineHeightCurrent, fontSizeCurrent.toDouble(), currentColor, mainFontFamily, maxWidth);
      }
      for (int j = 0; j < spec.nextLines.length; j++) {
        drawCentered(spec.nextLines[j], nextTop + j * lineHeightOther, fontSizeOther.toDouble(), otherColor, otherFontFamily, maxWidth);
      }

      final image = await recorder.endRecording().toImage(bgWidth, bgHeight);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final path = '${outDir.path}/lyric_${i.toString().padLeft(3, '0')}.png';
      await File(path).writeAsBytes(byteData!.buffer.asUint8List(), flush: true);
      paths.add(path);
    }

    return paths;
  }

  /// Generate lyric timing specifications
  static List<_LyricSpec> _generateLyricSpecs(List<dynamic> lyricsData, double durationSeconds) {
    final specs = <_LyricSpec>[];
    if (lyricsData.isEmpty) return specs;

    // Add preroll if first lyric doesn't start at 0
    final first = lyricsData.first;
    final firstStart = _parseTime(first["start_time"] ?? "0:0:0");
    if (firstStart > 0) {
      final currentLines = _wrapText(first["text"] ?? "", maxChars: 20);
      specs.add(_LyricSpec(
        start: 0.0,
        end: firstStart,
        currentLines: currentLines,
        prevLines: const [],
        nextLines: lyricsData.length > 1 ? _wrapText(lyricsData[1]["text"] ?? "", maxChars: 25) : const [],
        isPreroll: true,
      ));
    }

    // Add main lyric segments
    for (int i = 0; i < lyricsData.length; i++) {
      final item = lyricsData[i];
      final start = _parseTime(item["start_time"] ?? "0:0:0");
      double end = _parseTime(item["end_time"] ?? "0:0:0");
      if (end == 0.0 && i == lyricsData.length - 1) {
        end = durationSeconds;
      }

      final currentLines = _wrapText(item["text"] ?? "", maxChars: 20);
      final prevLines = i > 0 ? _wrapText(lyricsData[i - 1]["text"] ?? "", maxChars: 25) : <String>[];
      final nextLines = i < lyricsData.length - 1 ? _wrapText(lyricsData[i + 1]["text"] ?? "", maxChars: 25) : <String>[];

      specs.add(_LyricSpec(
        start: start,
        end: math.max(start, end),
        currentLines: currentLines,
        prevLines: prevLines,
        nextLines: nextLines,
      ));
    }

    return specs;
  }

  /// Execute FFmpeg command with optional lyric overlays (as PNG inputs)
  static void _executeSimpleFFmpegCommand({
    required VideoCreationParams params,
    required _VideoLayout layout,
    required double durationSeconds,
    List<_LyricSpec>? lyricSpecs,
    List<String>? lyricPngs,
    String? metaPng,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    // Build base video composition
    final List<String> filterComplex = [];

    // Create black background
    filterComplex.add("color=black:s=${bgWidth}x${bgHeight}:d=${durationSeconds.toStringAsFixed(3)}[bg]");

    // Scale user image (no circular mask for speed)
    filterComplex.add("[1:v]scale=$imageSize:$imageSize[user]");

    // Scale border
    filterComplex.add("[2:v]scale=${imageSize + 18}:${imageSize + 18}[border]");

    // Overlay border on background
    filterComplex.add("[bg][border]overlay=x=${layout.userX - 10}:y=${layout.userY - 10}[step0]");

    // Overlay user image
    filterComplex.add("[step0][user]overlay=x=${layout.userX}:y=${layout.userY}[step1]");

    // Scale and overlay logo
    filterComplex.add("[3:v]scale=$logoSize:$logoSize[logo]");
    filterComplex.add("[step1][logo]overlay=x=${layout.logoX}:y=${layout.logoY}[step2]");

    // SKIP WAVEFORM FOR SPEED - just use step2 as base
    filterComplex.add("[4:v]scale=$bgWidth:${layout.waveformHeight}[waveform_scaled]");
    filterComplex.add("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]");
    filterComplex.add("[step2][waveform]overlay=x=0:y=${layout.waveformY}:repeatlast=0[base]");
    // Add meta overlay always-on if provided
    String currentLabel = "step2"; // Start from step2, skip waveform
    int nextInputIndex = 5; // after audio (audio is now input 4)
    if (metaPng != null) {
      filterComplex.add("[$currentLabel][${nextInputIndex}:v]overlay=x=0:y=0[meta]");
      currentLabel = "meta";
      nextInputIndex += 1;
    }

    // AGGRESSIVE: Use only 5-8 lyric overlays max, regardless of input count
    if (lyricSpecs != null && lyricPngs != null && lyricSpecs.length == lyricPngs.length && lyricPngs.isNotEmpty) {
      final maxOverlays = 6; // Hard limit for speed
      final step = (lyricPngs.length / maxOverlays).ceil();
      int overlayCount = 0;

      for (int i = 0; i < lyricPngs.length && overlayCount < maxOverlays; i += step) {
        final inputLabel = "${nextInputIndex + overlayCount}:v";
        final nextLabel = "o$overlayCount";
        final s = lyricSpecs[i].start.toStringAsFixed(3);
        final e = lyricSpecs[i].end.toStringAsFixed(3);
        filterComplex.add("[$currentLabel][$inputLabel]overlay=x=0:y=0:enable='between(t,$s,$e)'[$nextLabel]");
        currentLabel = nextLabel;
        overlayCount++;
      }
    }

    final filterComplexArg = filterComplex.join(";");

    // Build command - SKIP WAVEFORM INPUT FOR SPEED
    final cmd = <String>[
      "-y",
      "-f",
      "lavfi",
      "-i",
      "anullsrc=channel_layout=stereo:sample_rate=48000", // 0
      "-i",
      params.userImageFile, // 1
      "-i",
      params.borderFile, // 2
      "-i",
      params.brandLogoFile, // 3
      "-i",
      params.waveformVideo, // 4
      "-i",
      params.audioFile, // 4 (was 5)
    ];

    // Add looping inputs for meta and lyric PNGs (after the audio to keep 5 as audio)
    if (metaPng != null) {
      cmd.addAll(["-loop", "1", "-t", durationSeconds.toStringAsFixed(3), "-i", metaPng]);
    }
    if (lyricPngs != null && lyricPngs.isNotEmpty) {
      // AGGRESSIVE: Only add max 6 PNG inputs
      const maxOverlays = 6;
      final step = (lyricPngs.length / maxOverlays).ceil();
      int overlayCount = 0;

      for (int i = 0; i < lyricPngs.length && overlayCount < maxOverlays; i += step) {
        cmd.addAll(["-loop", "1", "-t", durationSeconds.toStringAsFixed(3), "-i", lyricPngs[i]]);
        overlayCount++;
      }
    }

    cmd.addAll([
      "-filter_complex",
      filterComplexArg,
      "-map",
      "[$currentLabel]",
      "-map",
      "5:a",
      "-c:v",
      "libx264",
      "-preset",
      "ultrafast",
      "-crf",
      "42",
      "-pix_fmt",
      "yuv420p",
      "-c:a",
      "aac",
      "-b:a",
      "48k",
      params.outputPath,
    ]);

    // cmd.addAll([
    //   "-filter_complex",
    //   filterComplexArg,
    //   "-map",
    //   "[$currentLabel]",
    //   "-map",
    //   "4:a", // Audio is now input 4
    //   "-t",
    //   durationSeconds.toStringAsFixed(3),
    //   "-shortest",
    //   "-c:v",
    //   "libx264",
    //   "-preset",
    //   "ultrafast",
    //   "-tune",
    //   "fastdecode",
    //   "-crf",
    //   "40", // Higher CRF for faster encoding
    //   "-pix_fmt",
    //   "yuv420p",
    //   "-threads",
    //   "0", // Use all CPU cores
    //   "-c:a",
    //   "aac",
    //   "-b:a",
    //   "64k", // Lower audio bitrate
    //   "-ac",
    //   "1", // Mono audio for speed
    //   params.outputPath,
    // ]);

    log("Running FFmpeg command: ${cmd.join(' ')}");

    FFmpegKit.executeWithArgumentsAsync(
      cmd,
      (s) async {
        log("FFmpeg completed: ${await s.getState()} ${await s.getReturnCode()}");
        onSuccess?.call(params.outputPath);
      },
      (logLine) => log(logLine.getMessage()),
      (stats) => log("time: ${stats.getTime()} ms"),
    );
  }
}

// Data classes
class _VideoLayout {
  final int userX, userY, logoX, logoY;
  final double textX, textY;
  final int waveformHeight, waveformY, lyricsStartY;

  _VideoLayout({
    required this.userX,
    required this.userY,
    required this.logoX,
    required this.logoY,
    required this.textX,
    required this.textY,
    required this.waveformHeight,
    required this.waveformY,
    required this.lyricsStartY,
  });
}

class _LyricSpec {
  final double start, end;
  final List<String> currentLines, prevLines, nextLines;
  final bool isPreroll;

  _LyricSpec({
    required this.start,
    required this.end,
    required this.currentLines,
    required this.prevLines,
    required this.nextLines,
    this.isPreroll = false,
  });
}
